import { cn } from "@/libs/utils";
import { JSX } from "react";

type HighlightProps = {
  children: JSX.Element;
  className?: string;
};
function Highlight({ children, className }: HighlightProps) {
  return (
    <div className="relative inline-flex h-12 w-12 items-center justify-center rounded-lg border border-white border-opacity-10 bg-white bg-opacity-5 p-1.5">
      <div
        className={cn(
          "inline-flex h-9 w-9 items-center justify-center rounded-md bg-neutral-600 shadow",
          className
        )}
      >
        {children}
      </div>
    </div>
  );
}
export default Highlight;
