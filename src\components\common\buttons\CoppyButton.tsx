import { useCopyToClipboard } from '@/hooks/useCopyToClipboard';
import React from 'react';
import { TextButton } from '.';
import { FaCheck, FaCopy } from 'react-icons/fa';

interface CopyButtonProps {
  textToCopy: string;
}

const CopyButton: React.FC<CopyButtonProps> = ({ textToCopy }) => {
  const { copiedText, copyToClipboard } = useCopyToClipboard();


  const handleCopy = () => {
    copyToClipboard(textToCopy);
  };
  return (
    <TextButton onClick={handleCopy}>
      {copiedText === textToCopy ? (
        <FaCheck className="size-4 text-green-500" />
      ) : (
        <FaCopy className="size-4 stroke-neutral-400 hover:text-neutral-300" />
      )}
    </TextButton>
  )
};

export default CopyButton;