{"name": "education-management-system", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write .", "format:check": "prettier --check .", "preview": "vite preview"}, "dependencies": {"axios": "^1.10.0", "clsx": "^2.1.1", "lucide-react": "^0.344.0", "path": "^0.12.7", "react": "^19.0.0", "react-dom": "^19.0.0", "react-router-dom": "^6.20.1", "react-toastify": "^11.0.5", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/js": "^9.9.1", "@tailwindcss/postcss": "^4.0.0-alpha.5", "@types/node": "^22.14.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.5.11", "tailwindcss": "^4.0.0-alpha.5", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}