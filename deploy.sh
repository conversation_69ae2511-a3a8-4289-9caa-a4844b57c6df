#!/bin/bash

# === Configuration ===
PROJECT_NAME="NLS-fe"
PROJECT_DIR="/home/<USER>/$PROJECT_NAME"   # directory containing the source code
DEPLOY_DIR="/var/www/html/$PROJECT_NAME"           # target directory where <PERSON><PERSON><PERSON> serves the build
BRANCH="main"                                 # git branch to pull

# === Begin Deployment ===
echo "Pulling latest code from Git..."
git pull origin "$BRANCH"

echo "Installing dependencies..."
yarn install

echo "Building project..."
yarn run build || { echo "Build failed"; exit 1; }

echo "Removing old deployment directory..."
sudo rm -rf "$DEPLOY_DIR"

echo "Copying build folder to deployment directory..."
sudo cp -r "$PROJECT_DIR/dist" "$DEPLOY_DIR"

echo "Deployment completed successfully."
