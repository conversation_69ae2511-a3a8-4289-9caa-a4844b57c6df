// import { USER_ROLE, UserRoleType } from "@/constants/constants";

// utils/cookieAuth.ts
export interface User {
  id: string;
  permissions: string[];
  // role: UserRoleType;
  username: string;
  description?: string;
}

// Cookie utilities
export const setCookie = (
  name: string,
  value: string,
  options: {
    maxAge?: number;
    secure?: boolean;
    sameSite?: 'strict' | 'lax' | 'none';
    path?: string;
  } = {}
): void => {
  const {
    maxAge = 7 * 24 * 60 * 60, // 7 days default
    secure = true,
    sameSite = 'strict',
    path = '/',
  } = options;

  let cookieString = `${name}=${encodeURIComponent(value)}`;
  cookieString += `; Max-Age=${maxAge}`;
  cookieString += `; Path=${path}`;
  cookieString += `; SameSite=${sameSite}`;

  if (secure) {
    cookieString += '; Secure';
  }

  document.cookie = cookieString;
};

export const getCookie = (name: string): string | null => {
  const nameEQ = name + '=';
  const ca = document.cookie.split(';');

  for (let i = 0; i < ca.length; i++) {
    let c = ca[i];
    while (c.charAt(0) === ' ') c = c.substring(1, c.length);
    if (c.indexOf(nameEQ) === 0) {
      return decodeURIComponent(c.substring(nameEQ.length, c.length));
    }
  }
  return null;
};

export const deleteCookie = (name: string, path: string = '/'): void => {
  document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=${path}; SameSite=strict`;
};

// Auth-specific functions
export const getCurrentUser = (): User | null => {
  const userData = getCookie('userData');
  if (!userData) return null;

  try {
    return JSON.parse(userData);
  } catch (error) {
    console.error('Error parsing user data:', error);
    return null;
  }
};

export const setAuthData = (token: string, user: User): void => {
  // Note: In production, token should be set as httpOnly cookie from server
  // This is just for demo - client-side cookies are still accessible to JS
  setCookie('authToken', token, {
    maxAge: 24 * 60 * 60, // 24 hours
    secure: true,
    sameSite: 'strict',
  });

  setCookie('userData', JSON.stringify(user), {
    maxAge: 24 * 60 * 60, // 24 hours
    secure: true,
    sameSite: 'strict',
  });
};

export const clearAuthData = (): void => {
  deleteCookie('authToken');
  deleteCookie('userData');
};

export const isAuthenticated = (): boolean => {
  const token = getCookie('authToken');
  return !!token && !isTokenExpired(token);
};

export const isTokenExpired = (token: string): boolean => {
  try {
    const payload = JSON.parse(atob(token.split('.')[1]));
    const currentTime = Math.floor(Date.now() / 1000);
    return payload.exp < currentTime;
  } catch (error) {
    console.error('Error checking token expiration:', error);
    return true;
  }
};

export const hasPermission = (permission: string): boolean => {
  const user = getCurrentUser();
  return user?.permissions.includes(permission) || false;
};

export const hasRole = (role: UserRoleType): boolean => {
  const user = getCurrentUser();
  return user?.role === role;
};

export const getUserRole = (): string | null => {
  const user = getCurrentUser();
  return user?.role || null;
};

export const getUserPermissions = (): string[] => {
  const user = getCurrentUser();
  return user?.permissions || [];
};

// API interceptor for automatic cookie handling
export const apiRequest = async (
  url: string,
  options: RequestInit = {}
): Promise<Response> => {
  const defaultOptions: RequestInit = {
    credentials: 'include', // This ensures cookies are sent with requests
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  };

  const response = await fetch(url, defaultOptions);

  // Check if token expired based on response
  if (response.status === 401) {
    clearAuthData();
    window.location.href = '/login';
  }

  return response;
};

// Setup auto logout when token expires
export const setupTokenExpirationCheck = (callback?: () => void): void => {
  const token = getCookie('authToken');
  if (!token) return;

  try {
    const payload = JSON.parse(atob(token.split('.')[1]));
    const expirationTime = payload.exp * 1000;
    const currentTime = Date.now();
    const timeUntilExpiration = expirationTime - currentTime;

    if (timeUntilExpiration > 0) {
      setTimeout(() => {
        clearAuthData();
        if (callback) {
          callback();
        } else {
          window.location.href = '/login';
        }
      }, timeUntilExpiration);
    }
  } catch (error) {
    console.error('Error setting up token expiration check:', error);
  }
};

export const logout = (): void => {
  // Clear authentication data from cookies
  clearAuthData();

  // Redirect to login page
  window.location.href = '/signin';
};

// Thêm hàm tiện ích để kiểm tra nhanh các role phổ biến
// export const isAdmin = (): boolean => {
//   return hasRole(USER_ROLE.ADMIN);
// };

// export const isModerator = (): boolean => {
//   return hasRole(USER_ROLE.VIEWER) || hasRole(USER_ROLE.ADMIN);
// };
