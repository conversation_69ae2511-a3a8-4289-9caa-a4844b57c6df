import { JSX } from "react";
import { EmphasisButton, PrimaryButton } from "../commons/buttons";
import Modal from "../commons/modal";
import { cn } from "@/libs/utils";
import Highlight from "../highlight";
import { FaCircleInfo } from "react-icons/fa6";

type Props = {
  message?: string;
  title?: string;
  onConfirm?: () => void;
  onCancel?: () => void;
  type: "success" | "error" | "warning" | "info";
  show: boolean;
  onClose: () => void;
  config?: {
    confirmText?: string;
    cancelText?: string;
    confirmButton?: JSX.Element;
    cancelButton?: JSX.Element;
    messageElement?: JSX.Element;
  };
};

function MessageModal({
  message,
  title,
  onConfirm,
  onCancel,
  type,
  show,
  onClose,
  config,
}: Props) {
  return (
    <Modal
      className={cn("h-[250px]")}
      title={title ? title : type.toUpperCase()}
      isOpen={show}
      onClose={onClose}
    >
      <div className="flex flex-col justify-between h-full">
        {config?.messageElement ? (
          config.messageElement
        ) : (
          <div className="flex flex-col items-center justify-center gap-6">
            <Highlight
              className={cn(
                type === "error" && "bg-red-500",
                type === "success" && "bg-green-500",
                type === "warning" && "bg-yellow-500",
                type === "info" && "bg-blue-500"
              )}
            >
              <FaCircleInfo />
            </Highlight>
            <p
              className={cn(
                "text-base font-medium",
                type === "error" && "text-red-500",
                type === "success" && "text-green-500",
                type === "warning" && "text-yellow-500",
                type === "info" && "text-primary-500"
              )}
            >
              {message}
            </p>
          </div>
        )}
        <div className="flex justify-between items-center gap-2">
          {config?.cancelButton ? (
            config.cancelButton
          ) : (
            <EmphasisButton className="w-1/2" onClick={onCancel}>
              {config?.cancelText ? config.cancelText : "Cancel"}
            </EmphasisButton>
          )}
          {config?.confirmButton ? (
            config.confirmButton
          ) : (
            <PrimaryButton
              className={cn(
                "w-1/2",
                type === "error" && "bg-red-500 hover:bg-red-400",
                type === "warning" && "bg-yellow-500 hover:bg-yellow-400"
              )}
              onClick={onConfirm}
            >
              { }
              {config?.confirmText ? config.confirmText : "OK"}
            </PrimaryButton>
          )}
        </div>
      </div>
    </Modal>
  );
}

export default MessageModal;
