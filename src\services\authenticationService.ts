import { httpClient } from '@/helpers/api';

export const login = async ({
  email,
  password,
}: {
  email: string;
  password: string;
}) => {
  console.log('22222222222222222222333333', email, password);

  const client = await httpClient();
  const response = await client.post(
    '/auth/login',
    JSON.stringify({ email, password })
  );

  return response.data;
};

export const changePassword = async ({
  current_password,
  new_password,
}: {
  current_password: string;
  new_password: string;
}) => {
  const client = await httpClient();
  const response = await client.put('/change-password', {
    current_password,
    new_password,
  });

  return response.data;
};
