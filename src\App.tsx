import React from 'react';
import { Navigate, Route, BrowserRouter as Router, Routes } from 'react-router-dom';
import LoginForm from './components/Auth/LoginForm';
import Layout from './components/Layout/Layout';
import { AppProvider } from './contexts/AppContext';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import Dashboard from './pages/Dashboard';
import Students from './pages/Students';
import Teachers from './pages/Teachers';
import ToastProvider from './ToastProvider';

const AppRoutes: React.FC = () => {
  const { isAuthenticated } = useAuth();

  if (!isAuthenticated) {
    return <LoginForm />;
  }

  return (
    <Layout>
      <Routes>
        <Route path="/" element={<Dashboard />} />
        <Route path="/students" element={<Students />} />
        <Route path="/teachers" element={<Teachers/>} />
        <Route path="/courses" element={<div className="p-8 text-center text-gray-500">Trang <PERSON>h<PERSON>a học đang phát triển</div>} />
        <Route path="/majors" element={<div className="p-8 text-center text-gray-500">Trang Ngành nghề đang phát triển</div>} />
        <Route path="/events" element={<div className="p-8 text-center text-gray-500">Trang Sự kiện đang phát triển</div>} />
        <Route path="/media" element={<div className="p-8 text-center text-gray-500">Trang Thư viện media đang phát triển</div>} />
        <Route path="/reports" element={<div className="p-8 text-center text-gray-500">Trang Báo cáo đang phát triển</div>} />
        <Route path="/settings" element={<div className="p-8 text-center text-gray-500">Trang Cài đặt đang phát triển</div>} />
        <Route path="*" element={<Navigate to="/" replace />} />
      </Routes>
    </Layout>
  );
};

function App() {
  return (
    <AuthProvider>
      <AppProvider>
        <Router>
          <ToastProvider />
          <AppRoutes />
        </Router>
      </AppProvider>
    </AuthProvider>
  );
}

export default App;