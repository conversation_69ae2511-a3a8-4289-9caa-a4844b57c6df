import React, { ReactNode } from 'react';
import Sidebar from './Sidebar';
import Header from './Header';

const Layout: React.FC<{children:ReactNode}> = ({children}) => {
  return (
    <div className="flex h-screen bg-gray-50">
      <Sidebar />
      <div className="flex flex-1 flex-col overflow-hidden">
        <Header />
        <main className="flex-1 overflow-auto p-6">
          {children}
        </main>
      </div>
    </div>
  );
};

export default Layout;