import axios, { AxiosRequestConfig } from "axios";
import { toast } from "react-toastify";
import { getCookie } from "./auth";
// import { startCheckingTokenExpiration } from "./tokenUtils";

const API_URL = "http://localhost:3000/api/v1";

export const httpClient = async (contentType: string = "application/json") => {
  const headers = {
    Accept: "application/json",
    "Content-Type": contentType,
    "Access-Control-Allow-Origin": "*",
  };
  const config: AxiosRequestConfig = {
    headers: headers,
    baseURL: API_URL,
  };

  const instance = axios.create(config);

  instance.interceptors.request.use(
    (config) => {
      const token = getCookie("authToken");
      if (token) {
        config.headers["Authorization"] = `Bearer ${token}`;
      }
      return config;
    },
    (error) => {
      console.error(error);
      return Promise.reject(error);
    }
  );

  instance.interceptors.response.use(
    (response) => response,
    (error) => {
      console.error("Error from api:", error);

      // Display toast notifications for common error cases
      if (error.response && error.response.status === 403) {
        toast.error("Unauthorized");
        window.location.href = "/forbidden";
      } else if (error && error.response && error.response.data.message) {
        toast.error(error.response.data.message);
      }

      // Always return a rejected promise so React Query can catch it
      return Promise.reject(error);
    }
  );

  return instance;
};

// startCheckingTokenExpiration();
