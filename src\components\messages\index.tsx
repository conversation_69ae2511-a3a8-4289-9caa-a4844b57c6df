import { FaCheckCircle, FaInfoCircle } from "react-icons/fa";
import { FaCircleXmark, FaTriangleExclamation } from "react-icons/fa6";
import { TextButton } from "../common/buttons";
import Highlight from "../highlight";

type MessageProps = {
  message: string;
  typeError: "Error" | "Warning" | "Success";
};
export function Message({ message, typeError }: MessageProps) {
  return (
    <div className="flex items-start justify-between gap-4">
      <div className="flex items-center gap-4">
        <Highlight className="bg-warn-700">
          <>
            {typeError === "Error" && (
              <FaInfoCircle className="size-4 text-red-600" />
            )}
            {typeError === "Success" && (
              <FaCheckCircle className="size-4 text-green-600" />
            )}
            {typeError === "Warning" && (
              <FaTriangleExclamation className="size-4 text-yellow-600" />
            )}
          </>
        </Highlight>
        <div>
          <div className="flex flex-col gap-1">
            <span>{typeError}</span>
            {message && <span className="text-sm">{message}</span>}
          </div>
        </div>
      </div>
      <TextButton className="h-6 w-6 lg:h-6 lg:w-6">
        <FaCircleXmark className="size-4" />
      </TextButton>
    </div>
  );
}
