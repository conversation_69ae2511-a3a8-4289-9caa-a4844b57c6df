import { Filter, Search } from 'lucide-react';
import React from 'react';

interface TableColumn<T> {
  key: string;
  header: string;
  render?: (item: T) => React.ReactNode;
  width?: string;
}

interface DataTableProps<T> {
  title: string;
  description: string;
  data: T[];
  columns: TableColumn<T>[];
  keyField: keyof T;
  searchPlaceholder?: string;
  onSearch?: (query: string) => void;
  onAddNew?: () => void;
  addNewLabel?: string;
  onExport?: () => void;
  exportLabel?: string;
  renderActions?: (item: T) => React.ReactNode;
  showCheckboxes?: boolean;
  onRowSelect?: (selectedItems: T[]) => void;
  page?: number;
  pageSize?: number;
  total?: number;
  onPageChange?: (page: number) => void;
  selectedCount?: number;
  onPageSizeChange?: (size: number) => void;
  onDeleteSelected?: () => void;
}

const pageSizeOptions = [10, 20, 50, 100];

export function DataTable<T>({
  title,
  description,
  data,
  columns,
  keyField,
  searchPlaceholder = "Tìm kiếm...",
  onSearch,
  onAddNew,
  addNewLabel = "Thêm mới",
  onExport,
  exportLabel = "Xuất dữ liệu",
  renderActions,
  showCheckboxes = true,
  onRowSelect,
  page = 1,
  pageSize = 6,
  total = 0,
  onPageChange,
  selectedCount = 0,
  onPageSizeChange,
  onDeleteSelected,
}: DataTableProps) {
  const [selectedItems, setSelectedItems] = React.useState<T[]>([]);
  const [searchQuery, setSearchQuery] = React.useState('');

  const handleSelectAll = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.checked) {
      setSelectedItems([...data]);
    } else {
      setSelectedItems([]);
    }
    onRowSelect?.(selectedItems);
  };

  const handleSelectItem = (item: T, checked: boolean) => {
    if (checked) {
      setSelectedItems([...selectedItems, item]);
    } else {
      setSelectedItems(selectedItems.filter(i => i[keyField] !== item[keyField]));
    }
    onRowSelect?.(selectedItems);
  };

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
    onSearch?.(e.target.value);
  };

const totalPage = Math.max(1, Math.ceil(total / pageSize));

function getPagination(current: number, total: number): (number | string)[] {
  const visiblePages = 6;
  const pages: (number | string)[] = [];

  if (total <= visiblePages + 2) {
    for (let i = 1; i <= total; i++) pages.push(i);
    return pages;
  }

  pages.push(1); // luôn có trang đầu

  let start = current - Math.floor(visiblePages / 2);
  let end = current + Math.floor(visiblePages / 2);

  if (start < 2) {
    start = 2;
    end = start + visiblePages - 1;
  }

  if (end >= total) {
    end = total - 1;
    start = end - visiblePages + 1;
    if (start < 2) start = 2;
  }

  if (start > 2) pages.push('...');

  for (let i = start; i <= end; i++) {
    pages.push(i);
  }

  if (end < total - 1) pages.push('...');

  pages.push(total); // luôn có trang cuối

  return pages;
}

function PaginationEllipsis({ direction = 'right', onClick }: { direction?: 'left' | 'right', onClick?: () => void }) {
  return (
    <span
      className="relative select-none cursor-pointer group inline-flex items-center justify-center w-8 h-8"
      onClick={onClick}
      title={direction === 'right' ? 'Tiến nhanh' : 'Lùi nhanh'}
      style={{ minWidth: 32, minHeight: 32 }} // Đảm bảo kích thước cố định
    >
      <span className="absolute inset-0 flex items-center justify-center group-hover:opacity-0 transition-opacity duration-150">
        ...
      </span>
      <span className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 text-blue-500 transition-opacity duration-150 text-lg">
        {direction === 'right' ? '»' : '«'}
      </span>
    </span>
  );
}

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">{title}</h1>
          <p className="text-gray-600">{description}</p>
        </div>
        <div className="flex space-x-2">
          {onExport && (
            <button className="flex items-center space-x-2 rounded-lg border border-gray-300 bg-white px-4 py-2 text-gray-700 hover:bg-gray-50">
              <span>{exportLabel}</span>
            </button>
          )}
          {onAddNew && (
            <button 
              onClick={onAddNew}
              className="flex items-center space-x-2 rounded-lg bg-blue-600 px-4 py-2 text-white hover:bg-blue-700"
            >
              <span>{addNewLabel}</span>
            </button>
          )}
        </div>
      </div>

      {/* Search and filters */}
      <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
        <div className="flex items-center space-x-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              value={searchQuery}
              onChange={handleSearch}
              placeholder={searchPlaceholder}
              className="w-64 rounded-lg border border-gray-300 bg-white py-2 pl-10 pr-4 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
            />
          </div>
          <button className="flex items-center space-x-2 rounded-lg border border-gray-300 bg-white px-4 py-2 text-gray-700 hover:bg-gray-50">
            <Filter className="h-4 w-4" />
            <span>Lọc</span>
          </button>
        </div>
      </div>

      {/* Table */}
      <div className="overflow-hidden rounded-lg bg-white shadow">
        {/* Thanh thông tin phía trên bảng */}
        <div className="flex items-center gap-4 mb-2">
          <span className="text-gray-700 font-medium">Total: {total}</span>
          <select
            className="border-none shadow-none focus:ring-0 px-2 py-1"
            value={pageSize}
            onChange={e => onPageSizeChange?.(Number(e.target.value))}
          >
            {pageSizeOptions.map(opt => (
              <option key={opt} value={opt}>{opt} per page</option>
            ))}
          </select>
          {(selectedCount ?? 0) > 0 && (
            <>
              <span className="text-blue-600 font-medium">{selectedCount} selected</span>
              <button
                className="text-red-600 hover:underline"
                onClick={onDeleteSelected}
              >
                Delete
              </button>
            </>
          )}
        </div>

        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              {showCheckboxes && (
                <th className="w-12 px-6 py-3">
                  <input
                    type="checkbox"
                    className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    onChange={handleSelectAll}
                    checked={selectedItems.length === data.length && data.length > 0}
                  />
                </th>
              )}
              {columns.map((column) => (
                <th
                  key={column.key}
                  className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 ${column.width || ''}`}
                >
                  {column.header}
                </th>
              ))}
              {renderActions && (
                <th className="relative w-20 px-6 py-3">
                  <span className="sr-only">Thao tác</span>
                </th>
              )}
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200 bg-white">
            {data.map((item) => (
              <tr key={String(item[keyField])} className="hover:bg-gray-50">
                {showCheckboxes && (
                  <td className="px-6 py-4 whitespace-nowrap">
                    <input
                      type="checkbox"
                      className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      checked={selectedItems.some(i => i[keyField] === item[keyField])}
                      onChange={(e) => handleSelectItem(item, e.target.checked)}
                    />
                  </td>
                )}
                {columns.map((column) => (
                  <td key={column.key} className="px-6 py-4 whitespace-nowrap">
                    {column.render ? column.render(item) : String(item[column.key as keyof T] || '')}
                  </td>
                ))}
                {renderActions && (
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    {renderActions(item)}
                  </td>
                )}
              </tr>
            ))}
          </tbody>
        </table>
        
        {/* Pagination */}
        <div className="flex items-center justify-center border-t border-gray-200 bg-white px-4 py-3 sm:px-6">
          <nav className="isolate inline-flex -space-x-px rounded-md" aria-label="Pagination">
            {/* Prev page */}
            <button
              className="relative inline-flex items-center px-2 py-2 text-gray-400 hover:bg-gray-100 focus:z-20"
              disabled={page === 1}
              onClick={() => onPageChange?.(page - 1)}
              aria-label="Trang trước"
            >
              <svg width="16" height="16" fill="none" viewBox="0 0 16 16">
                <path d="M10 12L6 8L10 4" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </button>

            {/* Số trang */}
            {getPagination(page, totalPage).map((p, idx, arr) => {
              if (p === '...') {
                // Xác định direction dựa vào vị trí
                const direction = (idx < arr.length / 2) ? 'left' : 'right';
                return (
                  <PaginationEllipsis
                    key={`${p}_${idx}`}
                    direction={direction}
                    onClick={() => {
                      if (direction === 'left') {
                        onPageChange?.(Math.max(1, page - 5));
                      } else {
                        onPageChange?.(Math.min(totalPage, page + 5));
                      }
                    }}
                  />
                );
              }
              return (
                <button
                  key={p}
                  className={`inline-flex items-center justify-center w-10 h-10 mx-1 rounded transition-colors duration-150
                    ${page === p ? 'border border-blue-500 text-blue-600 bg-white' : 'text-gray-700 hover:bg-gray-100'}
                    cursor-pointer
                  `}
                  onClick={() => onPageChange?.(Number(p))}
                  disabled={page === p}
                >
                  {p}
                </button>
              );
            })}
            {/* Next page */}
            <button
              className="relative inline-flex items-center px-2 py-2 text-gray-400 hover:bg-gray-100 focus:z-20"
              disabled={page === totalPage}
              onClick={() => onPageChange?.(page + 1)}
              aria-label="Trang sau"
            >
              <svg width="16" height="16" fill="none" viewBox="0 0 16 16">
                <path d="M6 4L10 8L6 12" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </button>
          </nav>
        </div>
      </div>
    </div>
  );
}

export default DataTable;