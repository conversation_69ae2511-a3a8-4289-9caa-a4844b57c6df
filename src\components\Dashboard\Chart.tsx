import React from 'react';

interface ChartProps {
  title: string;
  subtitle?: string;
  data: Array<{ month: string; value: number }>;
}

const Chart: React.FC<ChartProps> = ({ title, subtitle, data }) => {
  const maxValue = Math.max(...data.map(d => d.value));
  
  return (
    <div className="rounded-xl bg-white p-6 shadow-sm">
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
        {subtitle && <p className="text-sm text-gray-500">{subtitle}</p>}
      </div>
      
      <div className="relative h-64">
        <div className="absolute inset-0 flex items-end justify-between space-x-2">
          {data.map((item, index) => (
            <div key={index} className="flex flex-1 flex-col items-center">
              <div
                className="w-full rounded-t bg-gradient-to-t from-blue-500 to-blue-400 transition-all hover:from-blue-600 hover:to-blue-500"
                style={{
                  height: `${(item.value / maxValue) * 100}%`,
                  minHeight: '4px',
                }}
              />
              <span className="mt-2 text-xs text-gray-500">{item.month}</span>
            </div>
          ))}
        </div>
        
        {/* Y-axis labels */}
        <div className="absolute left-0 top-0 flex h-full flex-col justify-between text-xs text-gray-400">
          <span>{maxValue}</span>
          <span>{Math.round(maxValue * 0.75)}</span>
          <span>{Math.round(maxValue * 0.5)}</span>
          <span>{Math.round(maxValue * 0.25)}</span>
          <span>0</span>
        </div>
      </div>
    </div>
  );
};

export default Chart;