import {
  BarChart3,
  BookOpen,
  Briefcase,
  ChevronLeft,
  ChevronRight,
  FileText,
  GraduationCap,
  Home,
  Phone,
  Users,
  Video,
  X,
} from 'lucide-react';
import React, { useState } from 'react';
import { NavLink } from 'react-router-dom';
import { useApp } from '../../contexts/AppContext';
import { cn } from '../../utils/utils';

const menuItems = [
  { icon: Home, label: 'Trang chủ', path: '/' },
  { icon: Users, label: 'Học viên', path: '/students' },
  { icon: GraduationCap, label: 'Giáo viên', path: '/teachers' },
  { icon: BookOpen, label: '<PERSON><PERSON><PERSON><PERSON> học & <PERSON> trình', path: '/courses' },
  { icon: Briefcase, label: 'Ngành nghề', path: '/majors' },
  { icon: FileText, label: 'Sự kiện', path: '/events' },
  { icon: Video, label: 'Thư viện media', path: '/media' },
  { icon: BarChart3, label: '<PERSON><PERSON><PERSON> c<PERSON>o & Thống kê', path: '/reports' },
  { icon: Phone, label: '<PERSON><PERSON><PERSON> đặt hệ thống', path: '/settings' },
];

const Sidebar: React.FC = () => {
  const { sidebarOpen, setSidebar } = useApp();
  const [collapsed, setCollapsed] = useState(false);

  return (
    <>
      {/* Mobile overlay */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
          onClick={() => setSidebar(false)}
        />
      )}

      {/* Sidebar */}
      <div
        className={cn(
          'fixed left-0 top-0 z-50 h-full bg-slate-900 shadow-lg transition-all duration-300 flex flex-col',
          collapsed ? 'w-20' : 'w-64',
          sidebarOpen ? 'translate-x-0' : '-translate-x-full',
          'lg:relative lg:translate-x-0'
        )}
      >
        {/* Header */}
        <div className={cn(
          'flex h-16 items-center justify-between px-4',
          collapsed ? 'justify-center' : ''
        )}>
          <div className="flex items-center space-x-2">
            <div className="flex h-8 w-8 items-center justify-center rounded overflow-hidden">
              <img src="/images/logo.png" alt="Logo" className="h-8 w-8 object-contain" />
            </div>
            {!collapsed && (
              <span className="text-lg font-semibold text-white">QLHV</span>
            )}
          </div>
          <div className="flex items-center space-x-2">
            <button
              className="text-white focus:outline-none"
              onClick={() => setCollapsed((prev) => !prev)}
              title={collapsed ? 'Mở rộng menu' : 'Thu gọn menu'}
            >
              {collapsed ? <ChevronRight size={24} /> : <ChevronLeft size={24} />}
            </button>
            <button
              onClick={() => setSidebar(false)}
              className="text-gray-400 hover:text-white lg:hidden"
            >
              <X className="h-5 w-5" />
            </button>
          </div>
        </div>

        {/* Navigation */}
        <nav className="mt-8 flex-1 px-2">
          <ul className="space-y-2">
            {menuItems.map((item) => (
              <li key={item.path}>
                <NavLink
                  to={item.path}
                  className={({ isActive }) =>
                    cn(
                      'flex items-center rounded-lg px-3 py-2.5 text-sm font-medium transition-colors gap-3',
                      isActive
                        ? 'bg-blue-600 text-white'
                        : 'text-gray-300 hover:bg-slate-800 hover:text-white',
                      collapsed ? 'justify-center px-2' : ''
                    )
                  }
                  onClick={() => window.innerWidth < 1024 && setSidebar(false)}
                >
                  <item.icon className="h-5 w-5 flex-shrink-0" />
                  {
                    !collapsed &&
                                 <span
                    className={cn(
                      'transition-all duration-200',
                      collapsed ? 'opacity-0 w-0 overflow-hidden' : 'opacity-100 w-auto ml-2'
                    )}
                  >
                    {item.label}
                  </span>
                  }
     
                </NavLink>
              </li>
            ))}
          </ul>
        </nav>
      </div>
    </>
  );
};

export default Sidebar;