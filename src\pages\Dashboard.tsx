import React from 'react';
import { Users, GraduationCap, BookOpen, TrendingUp } from 'lucide-react';
import StatsCard from '../components/Dashboard/StatsCard';
import Chart from '../components/Dashboard/Chart';
import Pie<PERSON>hart from '../components/Dashboard/PieChart';

const Dashboard: React.FC = () => {
  const studentData = [
    { month: 'T1', value: 45 },
    { month: 'T2', value: 52 },
    { month: 'T3', value: 48 },
    { month: 'T4', value: 61 },
    { month: 'T5', value: 55 },
    { month: 'T6', value: 67 },
    { month: 'T7', value: 73 },
    { month: 'T8', value: 78 },
    { month: 'T9', value: 82 },
    { month: 'T10', value: 89 },
    { month: 'T11', value: 95 },
    { month: 'T12', value: 100 },
  ];

  const genderData = [
    { label: 'Nam', value: 55, color: '#3B82F6' },
    { label: 'Nữ', value: 45, color: '#F97316' },
  ];

  const majorData = [
    { label: 'Công nghệ thông tin', value: 45, color: '#3B82F6' },
    { label: 'Kinh tế', value: 30, color: '#10B981' },
    { label: 'Ngoại ngữ', value: 15, color: '#F59E0B' },
    { label: 'Khác', value: 10, color: '#EF4444' },
  ];

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
        <p className="text-gray-600">Tổng quan hệ thống quản lý học viện</p>
      </div>

      {/* Stats cards */}
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
        <StatsCard
          title="Tổng số học viên"
          value="1,245"
          subtitle="Học viên đã đăng ký"
          icon={Users}
          trend={{ value: "12%", isPositive: true }}
          color="blue"
        />
        <StatsCard
          title="Tổng số giáo viên"
          value="32"
          subtitle="Giáo viên đang dạy"
          icon={GraduationCap}
          trend={{ value: "3%", isPositive: true }}
          color="green"
        />
        <StatsCard
          title="Khóa học đang hoạt động"
          value="8"
          subtitle="Khóa học đã mở lớp thực tế"
          icon={BookOpen}
          trend={{ value: "1", isPositive: true }}
          color="purple"
        />
        <StatsCard
          title="Tỷ lệ có việc làm"
          value="68%"
          subtitle="Tỷ lệ có việc làm sau tốt nghiệp"
          icon={TrendingUp}
          trend={{ value: "5%", isPositive: true }}
          color="orange"
        />
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        <Chart
          title="Số lượng học viên theo thời gian"
          subtitle="Biểu đồ thể hiện sự tăng trưởng học viên trong 12 tháng qua"
          data={studentData}
        />
        
        <PieChart
          title="Tỷ lệ việc làm"
          subtitle="Phân tích tỷ lệ việc làm của có việc làm sau tốt nghiệp"
          data={[
            { label: 'Đã có việc', value: 68, color: '#3B82F6' },
            { label: 'Chưa có việc', value: 32, color: '#F97316' },
          ]}
        />
      </div>

      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        <PieChart
          title="Phân bố học viên theo ngành nghề"
          subtitle="Số lượng học viên trong từng ngành nghề đào tạo"
          data={majorData}
        />
        
        <PieChart
          title="Phân bố học viên theo giới tính"
          subtitle="Tỷ lệ học viên nam và nữ trong toàn trung tâm"
          data={genderData}
        />
      </div>
    </div>
  );
};

export default Dashboard;