import { CompTypes, CompTypeValue } from "@/constants/type";
import { cn } from "@/libs/utils";
import React, {
  ButtonHTMLAttributes,
  ElementType,
  MouseEventHandler,
} from "react";

interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  className?: string;
  iconClassName?: string;
  variant?: CompTypeValue;
  responsive?: boolean;
  LeadingIcon?: ElementType | null;
  trailingIcon?: React.ReactNode;
  isLoading?: boolean;
  leading?: React.ReactNode;
  onClick?: MouseEventHandler<HTMLButtonElement>;
}

const Button: React.FC<ButtonProps> = ({
  className,
  iconClassName,
  variant = CompTypes.Primary,
  responsive = false,
  LeadingIcon = null,
  trailingIcon = null,
  isLoading = false,
  children,
  disabled,
  leading = null,
  onClick = () => {},
  ...rest
}) => {
  return (
    <button
      type="button"
      className={cn(
        "group inline-flex cursor-pointer items-center justify-center gap-2",
        "outline outline-2 outline-offset-4 outline-transparent",
        "rounded-lg font-medium transition-all duration-200 ease-out",
        !disabled && "focus:outline-none focus:ring-2 focus:ring-offset-2",
        responsive
          ? "p-2 text-xs lg:px-4 lg:py-3 lg:text-base lg:leading-tight"
          : "px-4 py-3 text-base leading-tight font-code",
        variant === CompTypes.Primary && [
          "bg-gradient-to-br from-primary-500 to-jellyfish-600 text-neutral-50",
          !disabled &&
            "hover:from-primary-600 hover:to-jellyfish-500 hover:text-primary-50 focus:ring-primary-500 hover:shadow-[0_0_15px_rgba(0,230,230,0.4)] hover:scale-105",
          disabled &&
            "opacity-60 hover:from-primary-500 hover:to-jellyfish-400",
        ],
        variant === CompTypes.Error && [
          "bg-red-700 text-neutral-100",
          !disabled &&
            "hover:bg-red-600 hover:text-neutral-50 hover:shadow-[0_0_15px_rgba(220,38,38,0.4)] hover:scale-105",
          !disabled && "active:bg-red-600 active:text-neutral-50",
          disabled && "bg-neutral-700 text-neutral-500 opacity-60",
        ],
        variant === CompTypes.Secondary && [
          "bg-secondary-500 text-neutral-50",
          !disabled &&
            "hover:bg-secondary-400 active:bg-secondary-500 hover:shadow-[0_0_15px_rgba(102,0,204,0.4)] hover:scale-105",
          disabled && "bg-neutral-700 text-neutral-500 opacity-60",
        ],
        variant === CompTypes.Tertiary && [
          "px-[15px] py-[11px]",
          "border border-primary-500 text-primary-500",
          !disabled &&
            "hover:border-primary-400 hover:text-primary-400 hover:shadow-[0_0_10px_rgba(0,230,230,0.2)] hover:scale-105",
          disabled && "border-neutral-700 text-neutral-700 opacity-60",
        ],
        variant === CompTypes.Text && [
          "text-neutral-50",
          !disabled &&
            "hover:bg-cosmic-accent/30 hover:text-neutral-100 hover:scale-105",
          disabled && "bg-transparent text-neutral-600 opacity-60",
        ],
        variant === CompTypes.Emphasis && [
          "bg-cosmic-accent text-neutral-100",
          !disabled &&
            "hover:bg-cosmic-highlight hover:text-primary-50 hover:shadow-[0_0_15px_rgba(33,97,242,0.4)] hover:scale-105",
          !disabled && "active:bg-cosmic-accent active:text-primary-50",
          disabled && "text-neutral-500 opacity-60",
        ],
        variant === CompTypes.Outlined && [
          "border border-cosmic-highlight text-cosmic-highlight",
          !disabled &&
            "hover:border-transparent hover:bg-cosmic-accent/30 hover:text-neutral-100 hover:shadow-[0_0_10px_rgba(33,97,242,0.3)] hover:scale-105",
          disabled &&
            "border-neutral-700 bg-transparent text-neutral-700 opacity-60",
        ],
        variant === CompTypes.Trailing && [
          "relative flex items-center overflow-hidden pl-6 pr-4",
          "bg-primary-600 text-neutral-50",
          !disabled &&
            "hover:bg-primary-500 hover:shadow-[0_0_15px_rgba(0,230,230,0.4)] hover:scale-105",
          disabled && "opacity-60",
        ],
        disabled && "cursor-not-allowed transition-none hover:scale-100",
        isLoading &&
          "relative overflow-hidden before:absolute before:inset-0 before:animate-pulse before:bg-white/10",
        className
      )}
      onClick={(e) => !disabled && onClick?.(e)}
      {...rest}
    >
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/10">
          <div className="h-5 w-5 animate-spin rounded-full border-2 border-primary-300 border-t-transparent"></div>
        </div>
      )}
      {LeadingIcon && (
        <LeadingIcon
          className={cn(
            "h-4 w-4 transition-all lg:h-5 lg:w-5",
            variant === CompTypes.Primary && [
              "stroke-primary-100",
              "group-hover:stroke-primary-200",
              "group-active:stroke-primary-100",
              "group-disabled:stroke-neutral-500",
            ],
            variant === CompTypes.Secondary && [
              "stroke-neutral-100",
              "group-hover:stroke-neutral-50",
              "group-active:stroke-neutral-100",
              "group-disabled:stroke-neutral-500",
            ],
            variant === CompTypes.Tertiary && [
              "stroke-primary-500",
              "group-hover:stroke-primary-400",
              "group-active:stroke-primary-500",
              "group-disabled:stroke-neutral-700",
            ],
            variant === CompTypes.Text && [
              "stroke-neutral-400",
              "group-hover:stroke-neutral-100",
              "group-active:stroke-neutral-100",
              "group-disabled:stroke-neutral-600",
            ],
            variant === CompTypes.Emphasis && [
              "stroke-neutral-100",
              "group-hover:stroke-neutral-50",
              "group-active:stroke-neutral-100",
              "group-disabled:stroke-neutral-500",
            ],
            variant === CompTypes.Outlined && [
              "stroke-cosmic-highlight",
              "group-hover:stroke-neutral-100",
              "group-active:stroke-neutral-100",
              "group-disabled:stroke-neutral-700",
            ],
            iconClassName
          )}
        />
      )}
      {leading}
      {variant === CompTypes.Trailing ? (
        <>
          <span className="z-10">{children}</span>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="17"
            viewBox="0 0 16 17"
            fill="none"
          >
            <path
              d="M6 12.5L10 8.5L6 4.5"
              stroke="white"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
          <div className="icon-button absolute -bottom-4 left-[19px] h-[46px] w-[100px] rounded-full blur-md bg-primary-500/50" />
        </>
      ) : (
        children
      )}
      {trailingIcon}
    </button>
  );
};

export default Button;

export const PrimaryButton = (props: ButtonProps) => <Button {...props} />;
export const SecondaryButton = (props: ButtonProps) => (
  <Button variant={CompTypes.Secondary} {...props} />
);
export const TertiaryButton = (props: ButtonProps) => (
  <Button variant={CompTypes.Tertiary} {...props} />
);
export const TextButton = (props: ButtonProps) => (
  <Button variant={CompTypes.Text} {...props} />
);
export const ErrorButton = (props: ButtonProps) => (
  <Button variant={CompTypes.Error} {...props} />
);
export const EmphasisButton = (props: ButtonProps) => (
  <Button variant={CompTypes.Emphasis} {...props} />
);
export const OutlinedButton = (props: ButtonProps) => (
  <Button variant={CompTypes.Outlined} {...props} />
);
export const TrailingButton = (props: ButtonProps) => (
  <Button variant={CompTypes.Trailing} {...props} />
);
