import { toast } from "react-toastify";
import { Message } from ".";

export function messageToast(
  message: string,
  type: "Success" | "Error" | "Warning"
) {
  const isMobileOrTablet =
    typeof window !== "undefined" && window.innerWidth < 1024;
  const position = isMobileOrTablet ? "top-right" : "bottom-left";
  if (type === "Error") {
    toast.error(<Message message={message} typeError={type} />, {
      icon: false,
      position,
    });
  }

  if (type === "Success") {
    toast.success(<Message message={message} typeError={type} />, {
      icon: false,
      position,
    });
  }

  if (type === "Warning") {
    toast.warn(<Message message={message} typeError={type} />, {
      icon: false,
      position,
    });
  }
}
